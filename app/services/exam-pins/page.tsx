"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, GraduationCap, Zap, Shield, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function ExamPinsPage() {
  const [selectedExam, setSelectedExam] = useState("")
  const [selectedSubject, setSelectedSubject] = useState("")
  const [quantity, setQuantity] = useState("1")
  const [isProcessing, setIsProcessing] = useState(false)

  const examTypes = [
    { name: "WAEC", code: "waec", price: 3500, description: "West African Examinations Council" },
    { name: "NECO", code: "neco", price: 1000, description: "National Examinations Council" },
    { name: "JAMB", code: "jamb", price: 4700, description: "Joint Admissions and Matriculation Board" },
    { name: "NABTEB", code: "nabteb", price: 1500, description: "National Business and Technical Examinations Board" },
  ]

  const subjects = {
    waec: [
      "Mathematics",
      "English Language",
      "Physics",
      "Chemistry",
      "Biology",
      "Economics",
      "Government",
      "Literature",
      "Geography",
      "History",
    ],
    neco: [
      "Mathematics",
      "English Language",
      "Physics",
      "Chemistry",
      "Biology",
      "Economics",
      "Government",
      "Literature",
      "Geography",
      "History",
    ],
    jamb: ["UTME Form", "Direct Entry Form"],
    nabteb: [
      "Mathematics",
      "English Language",
      "Physics",
      "Chemistry",
      "Biology",
      "Economics",
      "Government",
      "Literature",
      "Geography",
      "History",
    ],
  }

  const handlePurchase = async () => {
    setIsProcessing(true)
    await new Promise((resolve) => setTimeout(resolve, 2000))
    setIsProcessing(false)
    const examInfo = examTypes.find((exam) => exam.code === selectedExam)
    const totalAmount = examInfo ? examInfo.price * Number.parseInt(quantity) : 0
    alert(
      `Exam pin purchase successful! ${quantity} ${selectedExam.toUpperCase()} pin(s) purchased for ₦${totalAmount.toLocaleString()}`,
    )
  }

  const selectedExamDetails = examTypes.find((exam) => exam.code === selectedExam)
  const totalAmount = selectedExamDetails ? selectedExamDetails.price * Number.parseInt(quantity) : 0

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-emerald-50 to-green-50">
      {/* Header */}
      <div className="bg-white/90 backdrop-blur-lg border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Link href="/">
              <Button variant="ghost" size="sm" className="text-emerald-600 hover:text-emerald-700">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </Link>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-600 rounded-xl flex items-center justify-center">
                <GraduationCap className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">Buy Exam Pins</h1>
                <p className="text-sm text-emerald-600">WAEC, NECO, JAMB, NABTEB</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Purchase Form */}
          <div className="lg:col-span-2">
            <Card className="bg-white/90 backdrop-blur-lg border-2 border-emerald-100 shadow-xl rounded-3xl">
              <CardHeader className="pb-6">
                <CardTitle className="text-2xl text-gray-800 flex items-center">
                  <GraduationCap className="w-6 h-6 mr-3 text-emerald-600" />
                  Purchase Exam Pins
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Exam Type Selection */}
                <div>
                  <Label className="text-lg font-semibold text-gray-700 mb-4 block">Select Examination</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {examTypes.map((exam) => (
                      <button
                        key={exam.code}
                        onClick={() => {
                          setSelectedExam(exam.code)
                          setSelectedSubject("")
                        }}
                        className={`p-4 rounded-2xl border-2 transition-all duration-300 transform hover:scale-105 text-left ${
                          selectedExam === exam.code
                            ? "border-emerald-500 bg-emerald-50 shadow-lg"
                            : "border-gray-200 bg-white hover:border-emerald-300"
                        }`}
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="font-bold text-xl text-gray-800">{exam.name}</div>
                            <div className="text-sm text-gray-600 mt-1">{exam.description}</div>
                          </div>
                          <div className="text-right">
                            <div className="font-bold text-lg text-emerald-600">₦{exam.price.toLocaleString()}</div>
                            <div className="text-xs text-gray-500">per pin</div>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Subject Selection */}
                {selectedExam && subjects[selectedExam as keyof typeof subjects] && (
                  <div>
                    <Label className="text-lg font-semibold text-gray-700 mb-4 block">
                      Select Subject {selectedExam === "jamb" ? "/ Form Type" : ""}
                    </Label>
                    <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                      <SelectTrigger className="h-14 text-lg rounded-xl border-2 border-emerald-100 focus:border-emerald-500">
                        <SelectValue placeholder={`Choose ${selectedExam === "jamb" ? "form type" : "subject"}`} />
                      </SelectTrigger>
                      <SelectContent>
                        {subjects[selectedExam as keyof typeof subjects].map((subject) => (
                          <SelectItem key={subject} value={subject}>
                            {subject}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Quantity */}
                <div>
                  <Label htmlFor="quantity" className="text-lg font-semibold text-gray-700">
                    Quantity
                  </Label>
                  <div className="flex mt-2 space-x-4">
                    <div className="flex space-x-2">
                      {[1, 2, 3, 5, 10].map((num) => (
                        <button
                          key={num}
                          onClick={() => setQuantity(num.toString())}
                          className={`w-12 h-12 rounded-xl border-2 transition-all duration-300 transform hover:scale-105 ${
                            quantity === num.toString()
                              ? "border-emerald-500 bg-emerald-50 text-emerald-700 font-semibold"
                              : "border-gray-200 bg-white hover:border-emerald-300"
                          }`}
                        >
                          {num}
                        </button>
                      ))}
                    </div>
                    <Input
                      id="quantity"
                      type="number"
                      min="1"
                      max="50"
                      placeholder="Custom"
                      value={quantity}
                      onChange={(e) => setQuantity(e.target.value)}
                      className="h-12 w-24 text-lg rounded-xl border-2 border-emerald-100 focus:border-emerald-500"
                    />
                  </div>
                </div>

                {/* Purchase Button */}
                <Button
                  onClick={handlePurchase}
                  disabled={!selectedExam || (selectedExam !== "jamb" && !selectedSubject) || !quantity || isProcessing}
                  className="w-full h-14 text-lg bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isProcessing ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                      Processing...
                    </div>
                  ) : (
                    <>
                      <GraduationCap className="w-5 h-5 mr-2" />
                      Purchase {quantity} Pin{Number.parseInt(quantity) > 1 ? "s" : ""} - ₦
                      {totalAmount.toLocaleString()}
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Order Summary */}
            {selectedExamDetails && (
              <Card className="bg-white/90 backdrop-blur-lg border-2 border-emerald-100 shadow-xl rounded-3xl">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-800">Order Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Exam:</span>
                    <span className="font-semibold text-gray-800">{selectedExamDetails.name}</span>
                  </div>
                  {selectedSubject && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Subject:</span>
                      <span className="font-semibold text-gray-800">{selectedSubject}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600">Quantity:</span>
                    <span className="font-semibold text-gray-800">
                      {quantity} pin{Number.parseInt(quantity) > 1 ? "s" : ""}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Price per pin:</span>
                    <span className="font-semibold text-gray-800">₦{selectedExamDetails.price.toLocaleString()}</span>
                  </div>
                  <div className="border-t pt-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Total:</span>
                      <div className="font-bold text-2xl text-emerald-600">₦{totalAmount.toLocaleString()}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Features */}
            <Card className="bg-white/90 backdrop-blur-lg border-2 border-emerald-100 shadow-xl rounded-3xl">
              <CardHeader>
                <CardTitle className="text-xl text-gray-800">Why Choose Our Exam Pins?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  { icon: <Zap className="w-5 h-5" />, title: "Instant Delivery", desc: "Pins delivered immediately" },
                  { icon: <Shield className="w-5 h-5" />, title: "100% Genuine", desc: "Authentic exam pins only" },
                  { icon: <Clock className="w-5 h-5" />, title: "24/7 Available", desc: "Purchase anytime" },
                ].map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="w-10 h-10 bg-emerald-100 rounded-xl flex items-center justify-center text-emerald-600 flex-shrink-0">
                      {feature.icon}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-800">{feature.title}</div>
                      <div className="text-sm text-gray-600">{feature.desc}</div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Commission Info */}
            <Card className="bg-gradient-to-r from-emerald-500 to-green-600 text-white shadow-xl rounded-3xl">
              <CardContent className="p-6">
                <h3 className="text-xl font-bold mb-3">Highest Commission</h3>
                <p className="text-emerald-100 mb-4">
                  Earn 5-10% commission on every exam pin sale through your VendBoss link.
                </p>
                <div className="bg-white/20 rounded-xl p-3">
                  <div className="text-sm text-emerald-100">Example: ₦3,500 WAEC pin</div>
                  <div className="text-lg font-bold">You earn: ₦175-350</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
