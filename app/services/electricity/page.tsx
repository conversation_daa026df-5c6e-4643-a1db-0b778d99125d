"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Zap, Shield, Clock, Search } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function ElectricityPage() {
  const [selectedDisco, setSelectedDisco] = useState("")
  const [meterNumber, setMeterNumber] = useState("")
  const [amount, setAmount] = useState("")
  const [customerInfo, setCustomerInfo] = useState<any>(null)
  const [isValidating, setIsValidating] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  const discos = [
    { name: "Ikeja Electric", code: "ikeja", region: "Lagos" },
    { name: "Eko Electric", code: "eko", region: "Lagos" },
    { name: "Abuja Electric", code: "abuja", region: "FCT" },
    { name: "Kano Electric", code: "kano", region: "Kano" },
    { name: "Port Harcourt Electric", code: "phed", region: "Rivers" },
    { name: "Enugu Electric", code: "eedc", region: "Enugu" },
    { name: "Ibadan Electric", code: "ibedc", region: "Oyo" },
    { name: "Jos Electric", code: "jedc", region: "Plateau" },
  ]

  const quickAmounts = [1000, 2000, 5000, 10000, 15000, 20000]

  const validateMeter = async () => {
    if (!selectedDisco || !meterNumber) return

    setIsValidating(true)
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500))
    setCustomerInfo({
      name: "John Doe",
      address: "123 Main Street, Lagos",
      meterType: "Prepaid",
      tariff: "R2 - Residential",
    })
    setIsValidating(false)
  }

  const handlePayment = async () => {
    setIsProcessing(true)
    await new Promise((resolve) => setTimeout(resolve, 2000))
    setIsProcessing(false)
    alert(`Electricity payment successful! ₦${amount} credited to meter ${meterNumber}`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-emerald-50 to-green-50">
      {/* Header */}
      <div className="bg-white/90 backdrop-blur-lg border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Link href="/">
              <Button variant="ghost" size="sm" className="text-emerald-600 hover:text-emerald-700">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </Link>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-600 rounded-xl flex items-center justify-center">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">Pay Electricity</h1>
                <p className="text-sm text-emerald-600">All DISCOs supported</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Payment Form */}
          <div className="lg:col-span-2">
            <Card className="bg-white/90 backdrop-blur-lg border-2 border-emerald-100 shadow-xl rounded-3xl">
              <CardHeader className="pb-6">
                <CardTitle className="text-2xl text-gray-800 flex items-center">
                  <Zap className="w-6 h-6 mr-3 text-emerald-600" />
                  Pay Electricity Bill
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* DISCO Selection */}
                <div>
                  <Label className="text-lg font-semibold text-gray-700 mb-4 block">Select Distribution Company</Label>
                  <Select value={selectedDisco} onValueChange={setSelectedDisco}>
                    <SelectTrigger className="h-14 text-lg rounded-xl border-2 border-emerald-100 focus:border-emerald-500">
                      <SelectValue placeholder="Choose your DISCO" />
                    </SelectTrigger>
                    <SelectContent>
                      {discos.map((disco) => (
                        <SelectItem key={disco.code} value={disco.code}>
                          <div className="flex justify-between items-center w-full">
                            <span>{disco.name}</span>
                            <span className="text-sm text-gray-500 ml-4">{disco.region}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Meter Number */}
                <div>
                  <Label htmlFor="meter" className="text-lg font-semibold text-gray-700">
                    Meter Number
                  </Label>
                  <div className="flex mt-2 space-x-2">
                    <Input
                      id="meter"
                      type="text"
                      placeholder="Enter meter number"
                      value={meterNumber}
                      onChange={(e) => setMeterNumber(e.target.value)}
                      className="h-14 text-lg rounded-xl border-2 border-emerald-100 focus:border-emerald-500 flex-1"
                    />
                    <Button
                      onClick={validateMeter}
                      disabled={!selectedDisco || !meterNumber || isValidating}
                      className="h-14 px-6 bg-emerald-500 hover:bg-emerald-600 rounded-xl"
                    >
                      {isValidating ? (
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      ) : (
                        <Search className="w-5 h-5" />
                      )}
                    </Button>
                  </div>
                </div>

                {/* Customer Info */}
                {customerInfo && (
                  <Card className="bg-emerald-50 border-emerald-200 rounded-2xl">
                    <CardContent className="p-4">
                      <h4 className="font-semibold text-emerald-800 mb-2">Customer Information</h4>
                      <div className="space-y-1 text-sm">
                        <div>
                          <span className="font-medium">Name:</span> {customerInfo.name}
                        </div>
                        <div>
                          <span className="font-medium">Address:</span> {customerInfo.address}
                        </div>
                        <div>
                          <span className="font-medium">Meter Type:</span> {customerInfo.meterType}
                        </div>
                        <div>
                          <span className="font-medium">Tariff:</span> {customerInfo.tariff}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Amount Selection */}
                <div>
                  <Label className="text-lg font-semibold text-gray-700 mb-4 block">Select Amount</Label>
                  <div className="grid grid-cols-3 gap-3 mb-4">
                    {quickAmounts.map((quickAmount) => (
                      <button
                        key={quickAmount}
                        onClick={() => setAmount(quickAmount.toString())}
                        className={`p-3 rounded-xl border-2 transition-all duration-300 transform hover:scale-105 ${
                          amount === quickAmount.toString()
                            ? "border-emerald-500 bg-emerald-50 text-emerald-700 font-semibold"
                            : "border-gray-200 bg-white hover:border-emerald-300"
                        }`}
                      >
                        ₦{quickAmount.toLocaleString()}
                      </button>
                    ))}
                  </div>
                  <Input
                    type="number"
                    placeholder="Enter custom amount"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    className="h-14 text-lg rounded-xl border-2 border-emerald-100 focus:border-emerald-500"
                  />
                </div>

                {/* Payment Button */}
                <Button
                  onClick={handlePayment}
                  disabled={!selectedDisco || !meterNumber || !amount || !customerInfo || isProcessing}
                  className="w-full h-14 text-lg bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isProcessing ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                      Processing Payment...
                    </div>
                  ) : (
                    <>
                      <Zap className="w-5 h-5 mr-2" />
                      Pay ₦{amount ? Number.parseInt(amount).toLocaleString() : 0}
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Features */}
            <Card className="bg-white/90 backdrop-blur-lg border-2 border-emerald-100 shadow-xl rounded-3xl">
              <CardHeader>
                <CardTitle className="text-xl text-gray-800">Why Pay With Us?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  { icon: <Zap className="w-5 h-5" />, title: "Instant Credit", desc: "Immediate meter credit" },
                  { icon: <Shield className="w-5 h-5" />, title: "Secure Payment", desc: "Bank-grade security" },
                  { icon: <Clock className="w-5 h-5" />, title: "24/7 Service", desc: "Always available" },
                ].map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="w-10 h-10 bg-emerald-100 rounded-xl flex items-center justify-center text-emerald-600 flex-shrink-0">
                      {feature.icon}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-800">{feature.title}</div>
                      <div className="text-sm text-gray-600">{feature.desc}</div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Commission Info */}
            <Card className="bg-gradient-to-r from-emerald-500 to-green-600 text-white shadow-xl rounded-3xl">
              <CardContent className="p-6">
                <h3 className="text-xl font-bold mb-3">Earn Commission</h3>
                <p className="text-emerald-100 mb-4">
                  Earn 1-3% commission on every electricity payment through your VendBoss link.
                </p>
                <div className="bg-white/20 rounded-xl p-3">
                  <div className="text-sm text-emerald-100">Example: ₦10,000 payment</div>
                  <div className="text-lg font-bold">You earn: ₦100-300</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
