"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Wifi, Zap, Shield, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export default function DataPage() {
  const [selectedNetwork, setSelectedNetwork] = useState("")
  const [selectedPlan, setSelectedPlan] = useState("")
  const [phoneNumber, setPhoneNumber] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)

  const networks = [
    { name: "MTN", code: "mtn", color: "from-yellow-400 to-yellow-600" },
    { name: "Airtel", code: "airtel", color: "from-red-400 to-red-600" },
    { name: "Glo", code: "glo", color: "from-green-400 to-green-600" },
    { name: "9mobile", code: "9mobile", color: "from-emerald-400 to-emerald-600" },
  ]

  const dataPlans = {
    mtn: [
      { size: "1GB", validity: "30 days", price: 280, originalPrice: 300 },
      { size: "2GB", validity: "30 days", price: 560, originalPrice: 600 },
      { size: "5GB", validity: "30 days", price: 1400, originalPrice: 1500 },
      { size: "10GB", validity: "30 days", price: 2800, originalPrice: 3000 },
    ],
    airtel: [
      { size: "1GB", validity: "30 days", price: 275, originalPrice: 300 },
      { size: "2GB", validity: "30 days", price: 550, originalPrice: 600 },
      { size: "5GB", validity: "30 days", price: 1375, originalPrice: 1500 },
      { size: "10GB", validity: "30 days", price: 2750, originalPrice: 3000 },
    ],
    glo: [
      { size: "1GB", validity: "30 days", price: 270, originalPrice: 300 },
      { size: "2GB", validity: "30 days", price: 540, originalPrice: 600 },
      { size: "5GB", validity: "30 days", price: 1350, originalPrice: 1500 },
      { size: "10GB", validity: "30 days", price: 2700, originalPrice: 3000 },
    ],
    "9mobile": [
      { size: "1GB", validity: "30 days", price: 285, originalPrice: 300 },
      { size: "2GB", validity: "30 days", price: 570, originalPrice: 600 },
      { size: "5GB", validity: "30 days", price: 1425, originalPrice: 1500 },
      { size: "10GB", validity: "30 days", price: 2850, originalPrice: 3000 },
    ],
  }

  const handlePurchase = async () => {
    setIsProcessing(true)
    await new Promise((resolve) => setTimeout(resolve, 2000))
    setIsProcessing(false)
    const plan = dataPlans[selectedNetwork as keyof typeof dataPlans]?.find((p) => p.size === selectedPlan)
    alert(`Data purchase successful! ${selectedPlan} ${selectedNetwork.toUpperCase()} data sent to ${phoneNumber}`)
  }

  const selectedPlanDetails =
    selectedNetwork && selectedPlan
      ? dataPlans[selectedNetwork as keyof typeof dataPlans]?.find((p) => p.size === selectedPlan)
      : null

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-emerald-50 to-green-50">
      {/* Header */}
      <div className="bg-white/90 backdrop-blur-lg border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Link href="/">
              <Button variant="ghost" size="sm" className="text-emerald-600 hover:text-emerald-700">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </Link>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-600 rounded-xl flex items-center justify-center">
                <Wifi className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">Buy Data</h1>
                <p className="text-sm text-emerald-600">Data bundles at wholesale rates</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Purchase Form */}
          <div className="lg:col-span-2">
            <Card className="bg-white/90 backdrop-blur-lg border-2 border-emerald-100 shadow-xl rounded-3xl">
              <CardHeader className="pb-6">
                <CardTitle className="text-2xl text-gray-800 flex items-center">
                  <Wifi className="w-6 h-6 mr-3 text-emerald-600" />
                  Purchase Data Bundle
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Network Selection */}
                <div>
                  <Label className="text-lg font-semibold text-gray-700 mb-4 block">Select Network</Label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {networks.map((network) => (
                      <button
                        key={network.code}
                        onClick={() => {
                          setSelectedNetwork(network.code)
                          setSelectedPlan("")
                        }}
                        className={`p-4 rounded-2xl border-2 transition-all duration-300 transform hover:scale-105 ${
                          selectedNetwork === network.code
                            ? `border-emerald-500 bg-gradient-to-r ${network.color} text-white shadow-lg`
                            : "border-gray-200 bg-white hover:border-emerald-300"
                        }`}
                      >
                        <div className="text-center">
                          <div className="font-bold text-lg">{network.name}</div>
                          <div className="text-sm opacity-80">Data Plans</div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Data Plans */}
                {selectedNetwork && (
                  <div>
                    <Label className="text-lg font-semibold text-gray-700 mb-4 block">Select Data Plan</Label>
                    <div className="grid gap-3">
                      {dataPlans[selectedNetwork as keyof typeof dataPlans]?.map((plan) => (
                        <button
                          key={plan.size}
                          onClick={() => setSelectedPlan(plan.size)}
                          className={`p-4 rounded-2xl border-2 transition-all duration-300 transform hover:scale-105 text-left ${
                            selectedPlan === plan.size
                              ? "border-emerald-500 bg-emerald-50 shadow-lg"
                              : "border-gray-200 bg-white hover:border-emerald-300"
                          }`}
                        >
                          <div className="flex justify-between items-center">
                            <div>
                              <div className="font-bold text-lg text-gray-800">{plan.size}</div>
                              <div className="text-sm text-gray-600">{plan.validity}</div>
                            </div>
                            <div className="text-right">
                              <div className="font-bold text-lg text-emerald-600">₦{plan.price}</div>
                              <div className="text-sm text-gray-500 line-through">₦{plan.originalPrice}</div>
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Phone Number */}
                <div>
                  <Label htmlFor="phone" className="text-lg font-semibold text-gray-700">
                    Phone Number
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="08012345678"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="mt-2 h-14 text-lg rounded-xl border-2 border-emerald-100 focus:border-emerald-500"
                  />
                </div>

                {/* Purchase Button */}
                <Button
                  onClick={handlePurchase}
                  disabled={!selectedNetwork || !selectedPlan || !phoneNumber || isProcessing}
                  className="w-full h-14 text-lg bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isProcessing ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                      Processing...
                    </div>
                  ) : (
                    <>
                      <Zap className="w-5 h-5 mr-2" />
                      Purchase Data - ₦{selectedPlanDetails?.price || 0}
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Order Summary */}
            {selectedPlanDetails && (
              <Card className="bg-white/90 backdrop-blur-lg border-2 border-emerald-100 shadow-xl rounded-3xl">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-800">Order Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Network:</span>
                    <span className="font-semibold text-gray-800">{selectedNetwork.toUpperCase()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Data Size:</span>
                    <span className="font-semibold text-gray-800">{selectedPlan}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Validity:</span>
                    <span className="font-semibold text-gray-800">{selectedPlanDetails.validity}</span>
                  </div>
                  <div className="border-t pt-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Total:</span>
                      <div className="text-right">
                        <div className="font-bold text-xl text-emerald-600">₦{selectedPlanDetails.price}</div>
                        <div className="text-sm text-gray-500 line-through">₦{selectedPlanDetails.originalPrice}</div>
                      </div>
                    </div>
                  </div>
                  <div className="bg-emerald-50 rounded-xl p-3">
                    <div className="text-sm text-emerald-700 font-semibold">
                      You save: ₦{selectedPlanDetails.originalPrice - selectedPlanDetails.price}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Features */}
            <Card className="bg-white/90 backdrop-blur-lg border-2 border-emerald-100 shadow-xl rounded-3xl">
              <CardHeader>
                <CardTitle className="text-xl text-gray-800">Why Choose Our Data Service?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  {
                    icon: <Zap className="w-5 h-5" />,
                    title: "Instant Activation",
                    desc: "Data activated immediately",
                  },
                  { icon: <Shield className="w-5 h-5" />, title: "Best Rates", desc: "Wholesale pricing guaranteed" },
                  { icon: <Clock className="w-5 h-5" />, title: "24/7 Service", desc: "Always available" },
                ].map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="w-10 h-10 bg-emerald-100 rounded-xl flex items-center justify-center text-emerald-600 flex-shrink-0">
                      {feature.icon}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-800">{feature.title}</div>
                      <div className="text-sm text-gray-600">{feature.desc}</div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
