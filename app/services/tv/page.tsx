"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Tv, Zap, Shield, Clock, Search } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export default function TVPage() {
  const [selectedProvider, setSelectedProvider] = useState("")
  const [selectedPackage, setSelectedPackage] = useState("")
  const [smartCardNumber, setSmartCardNumber] = useState("")
  const [customerInfo, setCustomerInfo] = useState<any>(null)
  const [isValidating, setIsValidating] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  const providers = [
    { name: "DSTV", code: "dstv", color: "from-blue-500 to-blue-600" },
    { name: "GOtv", code: "gotv", color: "from-green-500 to-green-600" },
    { name: "Startimes", code: "startimes", color: "from-purple-500 to-purple-600" },
  ]

  const packages = {
    dstv: [
      { name: "DSTV Padi", price: 2150, duration: "1 Month", channels: "40+ channels" },
      { name: "DSTV Yanga", price: 2950, duration: "1 Month", channels: "60+ channels" },
      { name: "DSTV Confam", price: 5300, duration: "1 Month", channels: "75+ channels" },
      { name: "DSTV Compact", price: 9000, duration: "1 Month", channels: "105+ channels" },
      { name: "DSTV Compact Plus", price: 14250, duration: "1 Month", channels: "145+ channels" },
      { name: "DSTV Premium", price: 21000, duration: "1 Month", channels: "173+ channels" },
    ],
    gotv: [
      { name: "GOtv Smallie", price: 900, duration: "1 Month", channels: "25+ channels" },
      { name: "GOtv Jinja", price: 1900, duration: "1 Month", channels: "45+ channels" },
      { name: "GOtv Jolli", price: 2800, duration: "1 Month", channels: "65+ channels" },
      { name: "GOtv Max", price: 4150, duration: "1 Month", channels: "75+ channels" },
    ],
    startimes: [
      { name: "Nova", price: 900, duration: "1 Month", channels: "32+ channels" },
      { name: "Basic", price: 1800, duration: "1 Month", channels: "45+ channels" },
      { name: "Smart", price: 2500, duration: "1 Month", channels: "60+ channels" },
      { name: "Classic", price: 2700, duration: "1 Month", channels: "65+ channels" },
      { name: "Super", price: 4200, duration: "1 Month", channels: "75+ channels" },
    ],
  }

  const validateSmartCard = async () => {
    if (!selectedProvider || !smartCardNumber) return

    setIsValidating(true)
    await new Promise((resolve) => setTimeout(resolve, 1500))
    setCustomerInfo({
      name: "Jane Smith",
      status: "Active",
      currentPackage: "DSTV Compact",
      dueDate: "2024-02-15",
    })
    setIsValidating(false)
  }

  const handleSubscription = async () => {
    setIsProcessing(true)
    await new Promise((resolve) => setTimeout(resolve, 2000))
    setIsProcessing(false)
    const packageInfo = packages[selectedProvider as keyof typeof packages]?.find((p) => p.name === selectedPackage)
    alert(`TV subscription successful! ${selectedPackage} activated for smartcard ${smartCardNumber}`)
  }

  const selectedPackageDetails =
    selectedProvider && selectedPackage
      ? packages[selectedProvider as keyof typeof packages]?.find((p) => p.name === selectedPackage)
      : null

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-emerald-50 to-green-50">
      {/* Header */}
      <div className="bg-white/90 backdrop-blur-lg border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Link href="/">
              <Button variant="ghost" size="sm" className="text-emerald-600 hover:text-emerald-700">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </Link>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-600 rounded-xl flex items-center justify-center">
                <Tv className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">TV Subscription</h1>
                <p className="text-sm text-emerald-600">DSTV, GOtv, Startimes</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Subscription Form */}
          <div className="lg:col-span-2">
            <Card className="bg-white/90 backdrop-blur-lg border-2 border-emerald-100 shadow-xl rounded-3xl">
              <CardHeader className="pb-6">
                <CardTitle className="text-2xl text-gray-800 flex items-center">
                  <Tv className="w-6 h-6 mr-3 text-emerald-600" />
                  TV Subscription
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Provider Selection */}
                <div>
                  <Label className="text-lg font-semibold text-gray-700 mb-4 block">Select TV Provider</Label>
                  <div className="grid grid-cols-3 gap-4">
                    {providers.map((provider) => (
                      <button
                        key={provider.code}
                        onClick={() => {
                          setSelectedProvider(provider.code)
                          setSelectedPackage("")
                        }}
                        className={`p-4 rounded-2xl border-2 transition-all duration-300 transform hover:scale-105 ${
                          selectedProvider === provider.code
                            ? `border-emerald-500 bg-gradient-to-r ${provider.color} text-white shadow-lg`
                            : "border-gray-200 bg-white hover:border-emerald-300"
                        }`}
                      >
                        <div className="text-center">
                          <div className="font-bold text-lg">{provider.name}</div>
                          <div className="text-sm opacity-80">Cable TV</div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Smart Card Number */}
                <div>
                  <Label htmlFor="smartcard" className="text-lg font-semibold text-gray-700">
                    Smart Card Number
                  </Label>
                  <div className="flex mt-2 space-x-2">
                    <Input
                      id="smartcard"
                      type="text"
                      placeholder="Enter smart card number"
                      value={smartCardNumber}
                      onChange={(e) => setSmartCardNumber(e.target.value)}
                      className="h-14 text-lg rounded-xl border-2 border-emerald-100 focus:border-emerald-500 flex-1"
                    />
                    <Button
                      onClick={validateSmartCard}
                      disabled={!selectedProvider || !smartCardNumber || isValidating}
                      className="h-14 px-6 bg-emerald-500 hover:bg-emerald-600 rounded-xl"
                    >
                      {isValidating ? (
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      ) : (
                        <Search className="w-5 h-5" />
                      )}
                    </Button>
                  </div>
                </div>

                {/* Customer Info */}
                {customerInfo && (
                  <Card className="bg-emerald-50 border-emerald-200 rounded-2xl">
                    <CardContent className="p-4">
                      <h4 className="font-semibold text-emerald-800 mb-2">Customer Information</h4>
                      <div className="space-y-1 text-sm">
                        <div>
                          <span className="font-medium">Name:</span> {customerInfo.name}
                        </div>
                        <div>
                          <span className="font-medium">Status:</span> {customerInfo.status}
                        </div>
                        <div>
                          <span className="font-medium">Current Package:</span> {customerInfo.currentPackage}
                        </div>
                        <div>
                          <span className="font-medium">Due Date:</span> {customerInfo.dueDate}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Package Selection */}
                {selectedProvider && (
                  <div>
                    <Label className="text-lg font-semibold text-gray-700 mb-4 block">Select Package</Label>
                    <div className="grid gap-3">
                      {packages[selectedProvider as keyof typeof packages]?.map((pkg) => (
                        <button
                          key={pkg.name}
                          onClick={() => setSelectedPackage(pkg.name)}
                          className={`p-4 rounded-2xl border-2 transition-all duration-300 transform hover:scale-105 text-left ${
                            selectedPackage === pkg.name
                              ? "border-emerald-500 bg-emerald-50 shadow-lg"
                              : "border-gray-200 bg-white hover:border-emerald-300"
                          }`}
                        >
                          <div className="flex justify-between items-center">
                            <div>
                              <div className="font-bold text-lg text-gray-800">{pkg.name}</div>
                              <div className="text-sm text-gray-600">
                                {pkg.channels} • {pkg.duration}
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-bold text-xl text-emerald-600">₦{pkg.price.toLocaleString()}</div>
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Subscribe Button */}
                <Button
                  onClick={handleSubscription}
                  disabled={!selectedProvider || !selectedPackage || !smartCardNumber || !customerInfo || isProcessing}
                  className="w-full h-14 text-lg bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isProcessing ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                      Processing...
                    </div>
                  ) : (
                    <>
                      <Tv className="w-5 h-5 mr-2" />
                      Subscribe - ₦{selectedPackageDetails?.price.toLocaleString() || 0}
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Order Summary */}
            {selectedPackageDetails && (
              <Card className="bg-white/90 backdrop-blur-lg border-2 border-emerald-100 shadow-xl rounded-3xl">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-800">Subscription Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Provider:</span>
                    <span className="font-semibold text-gray-800">{selectedProvider.toUpperCase()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Package:</span>
                    <span className="font-semibold text-gray-800">{selectedPackage}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Duration:</span>
                    <span className="font-semibold text-gray-800">{selectedPackageDetails.duration}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Channels:</span>
                    <span className="font-semibold text-gray-800">{selectedPackageDetails.channels}</span>
                  </div>
                  <div className="border-t pt-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Total:</span>
                      <div className="font-bold text-2xl text-emerald-600">
                        ₦{selectedPackageDetails.price.toLocaleString()}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Features */}
            <Card className="bg-white/90 backdrop-blur-lg border-2 border-emerald-100 shadow-xl rounded-3xl">
              <CardHeader>
                <CardTitle className="text-xl text-gray-800">Why Choose Our TV Service?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  { icon: <Zap className="w-5 h-5" />, title: "Instant Activation", desc: "Immediate subscription" },
                  { icon: <Shield className="w-5 h-5" />, title: "Secure Payment", desc: "Protected transactions" },
                  { icon: <Clock className="w-5 h-5" />, title: "24/7 Support", desc: "Always here to help" },
                ].map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="w-10 h-10 bg-emerald-100 rounded-xl flex items-center justify-center text-emerald-600 flex-shrink-0">
                      {feature.icon}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-800">{feature.title}</div>
                      <div className="text-sm text-gray-600">{feature.desc}</div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
