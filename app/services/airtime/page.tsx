"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Phone, Zap, Shield, Clock } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export default function AirtimePage() {
  const [selectedNetwork, setSelectedNetwork] = useState("")
  const [phoneNumber, setPhoneNumber] = useState("")
  const [amount, setAmount] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)

  const networks = [
    { name: "MTN", code: "mtn", color: "from-yellow-400 to-yellow-600", commission: "2%" },
    { name: "Airtel", code: "airtel", color: "from-red-400 to-red-600", commission: "3%" },
    { name: "<PERSON><PERSON>", code: "glo", color: "from-green-400 to-green-600", commission: "2.5%" },
    { name: "9mobile", code: "9mobile", color: "from-emerald-400 to-emerald-600", commission: "3%" },
  ]

  const quickAmounts = [100, 200, 500, 1000, 2000, 5000]

  const handlePurchase = async () => {
    setIsProcessing(true)
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))
    setIsProcessing(false)
    alert(`Airtime purchase successful! ₦${amount} ${selectedNetwork} airtime sent to ${phoneNumber}`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-emerald-50 to-green-50">
      {/* Header */}
      <div className="bg-white/90 backdrop-blur-lg border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Link href="/">
              <Button variant="ghost" size="sm" className="text-emerald-600 hover:text-emerald-700">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </Link>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-600 rounded-xl flex items-center justify-center">
                <Phone className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">Buy Airtime</h1>
                <p className="text-sm text-emerald-600">Instant airtime for all networks</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Purchase Form */}
          <div className="lg:col-span-2">
            <Card className="bg-white/90 backdrop-blur-lg border-2 border-emerald-100 shadow-xl rounded-3xl">
              <CardHeader className="pb-6">
                <CardTitle className="text-2xl text-gray-800 flex items-center">
                  <Phone className="w-6 h-6 mr-3 text-emerald-600" />
                  Purchase Airtime
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Network Selection */}
                <div>
                  <Label className="text-lg font-semibold text-gray-700 mb-4 block">Select Network</Label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {networks.map((network) => (
                      <button
                        key={network.code}
                        onClick={() => setSelectedNetwork(network.code)}
                        className={`p-4 rounded-2xl border-2 transition-all duration-300 transform hover:scale-105 ${
                          selectedNetwork === network.code
                            ? `border-emerald-500 bg-gradient-to-r ${network.color} text-white shadow-lg`
                            : "border-gray-200 bg-white hover:border-emerald-300"
                        }`}
                      >
                        <div className="text-center">
                          <div className="font-bold text-lg">{network.name}</div>
                          <div className="text-sm opacity-80">{network.commission} commission</div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Phone Number */}
                <div>
                  <Label htmlFor="phone" className="text-lg font-semibold text-gray-700">
                    Phone Number
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="08012345678"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="mt-2 h-14 text-lg rounded-xl border-2 border-emerald-100 focus:border-emerald-500"
                  />
                </div>

                {/* Amount Selection */}
                <div>
                  <Label className="text-lg font-semibold text-gray-700 mb-4 block">Select Amount</Label>
                  <div className="grid grid-cols-3 gap-3 mb-4">
                    {quickAmounts.map((quickAmount) => (
                      <button
                        key={quickAmount}
                        onClick={() => setAmount(quickAmount.toString())}
                        className={`p-3 rounded-xl border-2 transition-all duration-300 transform hover:scale-105 ${
                          amount === quickAmount.toString()
                            ? "border-emerald-500 bg-emerald-50 text-emerald-700 font-semibold"
                            : "border-gray-200 bg-white hover:border-emerald-300"
                        }`}
                      >
                        ₦{quickAmount}
                      </button>
                    ))}
                  </div>
                  <Input
                    type="number"
                    placeholder="Enter custom amount"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    className="h-14 text-lg rounded-xl border-2 border-emerald-100 focus:border-emerald-500"
                  />
                </div>

                {/* Purchase Button */}
                <Button
                  onClick={handlePurchase}
                  disabled={!selectedNetwork || !phoneNumber || !amount || isProcessing}
                  className="w-full h-14 text-lg bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isProcessing ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                      Processing...
                    </div>
                  ) : (
                    <>
                      <Zap className="w-5 h-5 mr-2" />
                      Purchase Airtime - ₦{amount}
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Features */}
            <Card className="bg-white/90 backdrop-blur-lg border-2 border-emerald-100 shadow-xl rounded-3xl">
              <CardHeader>
                <CardTitle className="text-xl text-gray-800">Why Choose Our Airtime Service?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  {
                    icon: <Zap className="w-5 h-5" />,
                    title: "Instant Delivery",
                    desc: "Airtime delivered in seconds",
                  },
                  { icon: <Shield className="w-5 h-5" />, title: "100% Secure", desc: "Bank-grade security" },
                  { icon: <Clock className="w-5 h-5" />, title: "24/7 Available", desc: "Service never sleeps" },
                ].map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="w-10 h-10 bg-emerald-100 rounded-xl flex items-center justify-center text-emerald-600 flex-shrink-0">
                      {feature.icon}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-800">{feature.title}</div>
                      <div className="text-sm text-gray-600">{feature.desc}</div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Commission Info */}
            <Card className="bg-gradient-to-r from-emerald-500 to-green-600 text-white shadow-xl rounded-3xl">
              <CardContent className="p-6">
                <h3 className="text-xl font-bold mb-3">Earn Commission</h3>
                <p className="text-emerald-100 mb-4">
                  Earn 2-3% commission on every airtime purchase through your VendBoss link.
                </p>
                <div className="bg-white/20 rounded-xl p-3">
                  <div className="text-sm text-emerald-100">Example: ₦1000 airtime</div>
                  <div className="text-lg font-bold">You earn: ₦20-30</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
