"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, LinkIcon, Copy, Check, Share2, Globe, Sparkles } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export default function GenerateLinkPage() {
  const [businessName, setBusinessName] = useState("")
  const [ownerName, setOwnerName] = useState("")
  const [phoneNumber, setPhoneNumber] = useState("")
  const [generatedLink, setGeneratedLink] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)
  const [copied, setCopied] = useState(false)

  const generateLink = async () => {
    if (!businessName || !ownerName || !phoneNumber) return

    setIsGenerating(true)
    await new Promise((resolve) => setTimeout(resolve, 2000))

    const slug = businessName
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace(/[^a-z0-9-]/g, "")
    const link = `https://vendboss.com/${slug}-hub`
    setGeneratedLink(link)
    setIsGenerating(false)
  }

  const copyToClipboard = async () => {
    await navigator.clipboard.writeText(generatedLink)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const shareLink = async () => {
    if (navigator.share) {
      await navigator.share({
        title: `${businessName} - VendBoss Hub`,
        text: `Buy airtime, data, pay electricity bills and more at ${businessName}!`,
        url: generatedLink,
      })
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-emerald-50 to-green-50">
      {/* Header */}
      <div className="bg-white/90 backdrop-blur-lg border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Link href="/">
              <Button variant="ghost" size="sm" className="text-emerald-600 hover:text-emerald-700">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </Link>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-600 rounded-xl flex items-center justify-center">
                <LinkIcon className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">Generate Your VendBoss Link</h1>
                <p className="text-sm text-emerald-600">Create your personal vending website</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-2 gap-8">
          {/* Form */}
          <div>
            <Card className="bg-white/90 backdrop-blur-lg border-2 border-emerald-100 shadow-xl rounded-3xl">
              <CardHeader className="pb-6">
                <CardTitle className="text-2xl text-gray-800 flex items-center">
                  <Sparkles className="w-6 h-6 mr-3 text-emerald-600" />
                  Create Your VAS Empire
                </CardTitle>
                <p className="text-gray-600 mt-2">Fill in your details to generate your personalized vending website</p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="business" className="text-lg font-semibold text-gray-700">
                    Business Name
                  </Label>
                  <Input
                    id="business"
                    type="text"
                    placeholder="e.g., Queen4Real's Hub"
                    value={businessName}
                    onChange={(e) => setBusinessName(e.target.value)}
                    className="mt-2 h-14 text-lg rounded-xl border-2 border-emerald-100 focus:border-emerald-500"
                  />
                  <p className="text-sm text-gray-500 mt-1">This will be displayed on your website</p>
                </div>

                <div>
                  <Label htmlFor="owner" className="text-lg font-semibold text-gray-700">
                    Your Name
                  </Label>
                  <Input
                    id="owner"
                    type="text"
                    placeholder="e.g., Queen4Real"
                    value={ownerName}
                    onChange={(e) => setOwnerName(e.target.value)}
                    className="mt-2 h-14 text-lg rounded-xl border-2 border-emerald-100 focus:border-emerald-500"
                  />
                </div>

                <div>
                  <Label htmlFor="phone" className="text-lg font-semibold text-gray-700">
                    WhatsApp Number
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="e.g., ***********"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="mt-2 h-14 text-lg rounded-xl border-2 border-emerald-100 focus:border-emerald-500"
                  />
                  <p className="text-sm text-gray-500 mt-1">For direct customer contact</p>
                </div>

                <Button
                  onClick={generateLink}
                  disabled={!businessName || !ownerName || !phoneNumber || isGenerating}
                  className="w-full h-14 text-lg bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isGenerating ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                      Generating Your Empire...
                    </div>
                  ) : (
                    <>
                      <Globe className="w-5 h-5 mr-2" />
                      Generate My VendBoss Link
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Generated Link */}
            {generatedLink && (
              <Card className="mt-6 bg-gradient-to-r from-emerald-500 to-green-600 text-white shadow-xl rounded-3xl">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <Sparkles className="w-6 h-6 mr-2" />
                    Your VendBoss Link is Ready!
                  </h3>

                  <div className="bg-white/20 rounded-xl p-4 mb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 mr-4">
                        <p className="text-emerald-100 text-sm mb-1">Your Personal Vending Website:</p>
                        <p className="font-mono text-lg break-all">{generatedLink}</p>
                      </div>
                      <Button
                        onClick={copyToClipboard}
                        className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                        size="sm"
                      >
                        {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                      </Button>
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <Button
                      onClick={copyToClipboard}
                      className="flex-1 bg-white/20 hover:bg-white/30 text-white border-white/30"
                    >
                      <Copy className="w-4 h-4 mr-2" />
                      {copied ? "Copied!" : "Copy Link"}
                    </Button>
                    <Button
                      onClick={shareLink}
                      className="flex-1 bg-white/20 hover:bg-white/30 text-white border-white/30"
                    >
                      <Share2 className="w-4 h-4 mr-2" />
                      Share
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Preview */}
          <div>
            <Card className="bg-white/90 backdrop-blur-lg border-2 border-emerald-100 shadow-xl rounded-3xl sticky top-24">
              <CardHeader>
                <CardTitle className="text-xl text-gray-800">Website Preview</CardTitle>
              </CardHeader>
              <CardContent>
                {businessName ? (
                  <div className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-2xl p-6 border-2 border-emerald-200">
                    <div className="text-center mb-6">
                      <h2 className="text-2xl font-bold text-gray-800 mb-2">Welcome to {businessName}!</h2>
                      <p className="text-emerald-600 font-semibold">Powered by VendBoss</p>
                    </div>

                    <div className="grid grid-cols-2 gap-3 mb-6">
                      {[
                        { name: "Buy Airtime", icon: "📱" },
                        { name: "Buy Data", icon: "📶" },
                        { name: "Pay Electricity", icon: "⚡" },
                        { name: "TV Subscription", icon: "📺" },
                        { name: "Buy Exam Pins", icon: "🎓" },
                        { name: "More Services", icon: "➕" },
                      ].map((service, index) => (
                        <div
                          key={index}
                          className="bg-emerald-500 text-white p-3 rounded-xl text-center text-sm font-semibold"
                        >
                          <div className="text-lg mb-1">{service.icon}</div>
                          {service.name}
                        </div>
                      ))}
                    </div>

                    <div className="bg-emerald-500 text-white p-4 rounded-xl text-center mb-4">
                      <div className="font-bold text-lg">💰 Earn While You Sleep 🚀</div>
                    </div>

                    {ownerName && (
                      <div className="flex items-center space-x-3 mb-4">
                        <div className="w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center text-white font-bold">
                          {ownerName.slice(0, 2).toUpperCase()}
                        </div>
                        <div>
                          <div className="font-semibold text-gray-800">{ownerName}</div>
                          <div className="text-sm text-emerald-600">VendBoss Partner</div>
                        </div>
                      </div>
                    )}

                    {phoneNumber && (
                      <Button className="w-full bg-green-500 hover:bg-green-600 text-white rounded-xl">
                        💬 Chat via WhatsApp
                      </Button>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12 text-gray-500">
                    <Globe className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    <p>Fill in your details to see your website preview</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
