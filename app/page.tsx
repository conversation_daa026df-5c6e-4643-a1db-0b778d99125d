"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import {
  MessageCircle,
  Zap,
  Clock,
  TrendingUp,
  Star,
  Check,
  Menu,
  X,
  BarChart3,
  Globe,
  Phone,
  Wifi,
  Tv,
  GraduationCap,
  DollarSign,
  PlayCircle,
  ArrowRight,
  Users,
  Shield,
  Sparkles,
  ChevronRight,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"

export default function VendBossLanding() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const [currentTestimonial, setCurrentTestimonial] = useState(0)
  const [hoveredService, setHoveredService] = useState<number | null>(null)

  useEffect(() => {
    setIsVisible(true)
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % 3)
    }, 4000)
    return () => clearInterval(interval)
  }, [])

  const services = [
    {
      name: "Buy Airtime",
      icon: <Phone className="w-8 h-8" />,
      description: "Instant airtime for all networks",
      details: "MTN, Airtel, Glo, 9mobile",
      href: "/services/airtime",
      gradient: "from-emerald-400 via-green-500 to-emerald-600",
      iconBg: "from-emerald-100 to-green-100",
      commission: "2-5%",
    },
    {
      name: "Buy Data",
      icon: <Wifi className="w-8 h-8" />,
      description: "Data bundles at wholesale rates",
      details: "All networks, instant activation",
      href: "/services/data",
      gradient: "from-green-400 via-emerald-500 to-green-600",
      iconBg: "from-green-100 to-emerald-100",
      commission: "3-7%",
    },
    {
      name: "Pay Electricity",
      icon: <Zap className="w-8 h-8" />,
      description: "Electricity bills payment",
      details: "All DISCOs supported",
      href: "/services/electricity",
      gradient: "from-emerald-500 via-green-400 to-emerald-500",
      iconBg: "from-emerald-100 to-green-100",
      commission: "1-3%",
    },
    {
      name: "TV Subscription",
      icon: <Tv className="w-8 h-8" />,
      description: "Cable TV subscriptions",
      details: "DSTV, GOtv, Startimes",
      href: "/services/tv",
      gradient: "from-green-500 via-emerald-400 to-green-500",
      iconBg: "from-green-100 to-emerald-100",
      commission: "2-4%",
    },
    {
      name: "Buy Exam Pins",
      icon: <GraduationCap className="w-8 h-8" />,
      description: "Educational exam pins",
      details: "WAEC, NECO, JAMB",
      href: "/services/exam-pins",
      gradient: "from-emerald-600 via-green-500 to-emerald-600",
      iconBg: "from-emerald-100 to-green-100",
      commission: "5-10%",
    },
  ]

  const features = [
    {
      icon: <Globe className="w-12 h-12" />,
      title: "Instant Personal Website",
      description: "Get your branded vending website with custom domain in seconds. No technical skills needed.",
      color: "from-emerald-500 to-green-600",
      stats: "1000+ websites created",
    },
    {
      icon: <MessageCircle className="w-12 h-12" />,
      title: "WhatsApp Integration",
      description: "Serve customers directly via WhatsApp Business API with automated responses.",
      color: "from-green-500 to-emerald-600",
      stats: "24/7 automated chat",
    },
    {
      icon: <Clock className="w-12 h-12" />,
      title: "24/7 Automated Sales",
      description: "Your website processes transactions automatically, generating income while you sleep.",
      color: "from-emerald-600 to-green-500",
      stats: "₦2M+ processed daily",
    },
    {
      icon: <Shield className="w-12 h-12" />,
      title: "Secure Transactions",
      description: "Bank-grade security with instant transaction confirmations and fraud protection.",
      color: "from-green-600 to-emerald-500",
      stats: "99.9% uptime guarantee",
    },
  ]

  const testimonials = [
    {
      name: "Queen4Real",
      earnings: "₦147,300",
      message: "I've made over ₦147k this month! VendBoss completely transformed my business.",
      avatar: "/placeholder.svg?height=80&width=80&text=Q4R",
      location: "Lagos, Nigeria",
      transactions: "2,450+ transactions",
    },
    {
      name: "BossLady",
      earnings: "₦89,150",
      message: "My customers love the convenience. The WhatsApp integration is a game-changer!",
      avatar: "/placeholder.svg?height=80&width=80&text=BL",
      location: "Abuja, Nigeria",
      transactions: "1,890+ transactions",
    },
    {
      name: "TechGuru",
      earnings: "₦124,900",
      message: "The automated system works perfectly. I earn even when I'm not actively working!",
      avatar: "/placeholder.svg?height=80&width=80&text=TG",
      location: "Port Harcourt, Nigeria",
      transactions: "3,120+ transactions",
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-emerald-50 to-green-50 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Floating geometric shapes */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-emerald-200/30 to-green-200/30 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-r from-green-200/30 to-emerald-200/30 rounded-full blur-lg animate-bounce delay-1000"></div>
        <div className="absolute bottom-40 left-20 w-40 h-40 bg-gradient-to-r from-emerald-100/40 to-green-100/40 rounded-full blur-2xl animate-pulse delay-2000"></div>

        {/* Floating icons */}
        <div className="absolute top-32 left-1/4 text-emerald-300/40 animate-float">
          <Sparkles className="w-12 h-12" />
        </div>
        <div className="absolute top-60 right-1/3 text-green-300/40 animate-float delay-1000">
          <DollarSign className="w-10 h-10" />
        </div>
        <div className="absolute bottom-60 left-1/3 text-emerald-300/40 animate-float delay-2000">
          <Star className="w-14 h-14" />
        </div>
      </div>

      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-white/95 backdrop-blur-xl border-b border-emerald-100 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="w-14 h-14 bg-gradient-to-r from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center shadow-xl">
                  <span className="text-white font-bold text-2xl">V</span>
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-ping"></div>
              </div>
              <div>
                <span className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-green-700 bg-clip-text text-transparent">
                  VendBoss
                </span>
                <p className="text-xs text-emerald-600 font-semibold tracking-wider">VEND WHILE YOU SLEEP</p>
              </div>
            </div>

            {/* Desktop Menu */}
            <div className="hidden md:flex items-center space-x-8">
              <a
                href="#features"
                className="text-gray-700 hover:text-emerald-600 transition-all duration-300 font-medium relative group"
              >
                Features
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-emerald-500 transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a
                href="#services"
                className="text-gray-700 hover:text-emerald-600 transition-all duration-300 font-medium relative group"
              >
                Services
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-emerald-500 transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a
                href="#testimonials"
                className="text-gray-700 hover:text-emerald-600 transition-all duration-300 font-medium relative group"
              >
                Success Stories
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-emerald-500 transition-all duration-300 group-hover:w-full"></span>
              </a>
              <Button className="bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 px-6 py-2.5 rounded-xl">
                <Sparkles className="w-4 h-4 mr-2" />
                Start Earning Now
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <button className="md:hidden text-gray-800 p-2" onClick={() => setIsMenuOpen(!isMenuOpen)}>
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-white/98 backdrop-blur-xl border-t border-emerald-100 shadow-xl">
            <div className="px-4 py-6 space-y-4">
              <a
                href="#features"
                className="block text-gray-700 hover:text-emerald-600 transition-colors font-medium py-2"
              >
                Features
              </a>
              <a
                href="#services"
                className="block text-gray-700 hover:text-emerald-600 transition-colors font-medium py-2"
              >
                Services
              </a>
              <a
                href="#testimonials"
                className="block text-gray-700 hover:text-emerald-600 transition-colors font-medium py-2"
              >
                Success Stories
              </a>
              <Button className="w-full bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white py-3 rounded-xl">
                <Sparkles className="w-4 h-4 mr-2" />
                Start Earning Now
              </Button>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <div
              className={`transform transition-all duration-1000 ${isVisible ? "translate-y-0 opacity-100" : "translate-y-10 opacity-0"}`}
            >
              <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-100 to-green-100 rounded-full border border-emerald-200 mb-8 shadow-lg">
                <Star className="w-5 h-5 text-emerald-600 mr-3 animate-spin" />
                <span className="text-emerald-800 font-bold">3x Revenue Growth in 9 Months</span>
                <Sparkles className="w-5 h-5 text-green-600 ml-3" />
              </div>

              <h1 className="text-5xl md:text-7xl font-bold text-gray-800 mb-8 leading-tight">
                Your Personal
                <span className="block bg-gradient-to-r from-emerald-600 via-green-600 to-emerald-700 bg-clip-text text-transparent animate-gradient">
                  VAS Empire
                </span>
                <span className="block text-gray-700">Starts Here</span>
              </h1>

              <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
                Get your instant vending website and start selling airtime, data, TV subscriptions, electricity, and
                exam pins.
                <span className="block mt-3 text-emerald-700 font-bold text-3xl flex items-center justify-center">
                  <Sparkles className="w-8 h-8 mr-2 animate-pulse" />
                  Earn While You Sleep!
                  <Sparkles className="w-8 h-8 ml-2 animate-pulse" />
                </span>
              </p>

              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-xl px-12 py-6 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105 group"
                >
                  <PlayCircle className="mr-3 w-6 h-6 group-hover:animate-pulse" />
                  Start Your Empire
                  <ArrowRight className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform" />
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-2 border-emerald-500 text-emerald-700 hover:bg-emerald-50 text-xl px-12 py-6 rounded-2xl bg-white/80 backdrop-blur-sm shadow-xl hover:shadow-2xl transition-all duration-300"
                >
                  <Globe className="mr-3 w-6 h-6" />
                  Watch Demo
                </Button>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
                {[
                  { label: "Active Users", value: "10,000+", icon: <Users className="w-6 h-6" /> },
                  { label: "Monthly Revenue", value: "₦50M+", icon: <TrendingUp className="w-6 h-6" /> },
                  { label: "Success Rate", value: "99.9%", icon: <Shield className="w-6 h-6" /> },
                  { label: "Avg. Commission", value: "5.2%", icon: <BarChart3 className="w-6 h-6" /> },
                ].map((stat, index) => (
                  <div
                    key={index}
                    className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-xl border border-emerald-100 hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
                  >
                    <div className="text-emerald-600 mb-2 flex justify-center">{stat.icon}</div>
                    <div className="text-3xl font-bold text-gray-800 mb-1">{stat.value}</div>
                    <div className="text-sm text-gray-600 font-medium">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-emerald-50 to-green-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-6xl font-bold text-gray-800 mb-6">
              Premium
              <span className="bg-gradient-to-r from-emerald-600 to-green-700 bg-clip-text text-transparent">
                {" "}
                VAS Services
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Comprehensive suite of value-added services with competitive commission rates and instant processing.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <Link key={index} href={service.href}>
                <Card
                  className="group cursor-pointer bg-white/90 backdrop-blur-lg border-2 border-emerald-100 hover:border-emerald-300 transition-all duration-500 hover:shadow-2xl transform hover:scale-105 rounded-3xl overflow-hidden"
                  onMouseEnter={() => setHoveredService(index)}
                  onMouseLeave={() => setHoveredService(null)}
                >
                  <CardContent className="p-8 relative">
                    {/* Background gradient overlay */}
                    <div
                      className={`absolute inset-0 bg-gradient-to-br ${service.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
                    ></div>

                    {/* Service icon */}
                    <div
                      className={`w-20 h-20 bg-gradient-to-r ${service.iconBg} rounded-3xl flex items-center justify-center mb-6 group-hover:scale-110 transition-all duration-300 shadow-lg group-hover:shadow-xl`}
                    >
                      <div className="text-emerald-600 group-hover:scale-110 transition-transform duration-300">
                        {service.icon}
                      </div>
                    </div>

                    {/* Commission badge */}
                    <div className="absolute top-6 right-6 bg-gradient-to-r from-emerald-500 to-green-600 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg">
                      {service.commission}
                    </div>

                    <h3 className="text-2xl font-bold text-gray-800 mb-3 group-hover:text-emerald-700 transition-colors duration-300">
                      {service.name}
                    </h3>
                    <p className="text-gray-600 mb-2 text-lg">{service.description}</p>
                    <p className="text-emerald-600 font-semibold mb-6">{service.details}</p>

                    <div className="flex items-center text-emerald-600 font-semibold group-hover:translate-x-2 transition-transform duration-300">
                      <span>Get Started</span>
                      <ChevronRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>

                    {/* Hover effect indicator */}
                    <div
                      className={`absolute bottom-0 left-0 h-1 bg-gradient-to-r ${service.gradient} transition-all duration-500 ${hoveredService === index ? "w-full" : "w-0"}`}
                    ></div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-6xl font-bold text-gray-800 mb-6">
              Why Choose
              <span className="bg-gradient-to-r from-emerald-600 to-green-700 bg-clip-text text-transparent">
                {" "}
                VendBoss?
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Advanced features designed for serious entrepreneurs who want to scale their VAS business.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12">
            {features.map((feature, index) => (
              <Card
                key={index}
                className="group bg-white/90 backdrop-blur-lg border-2 border-emerald-100 hover:border-emerald-300 transition-all duration-500 hover:shadow-2xl transform hover:scale-105 rounded-3xl overflow-hidden"
              >
                <CardContent className="p-10 relative">
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
                  ></div>

                  <div className="flex items-start space-x-6">
                    <div className="w-24 h-24 bg-gradient-to-r from-emerald-100 to-green-100 rounded-3xl flex items-center justify-center group-hover:scale-110 transition-all duration-300 shadow-lg group-hover:shadow-xl flex-shrink-0">
                      <div className="text-emerald-600 group-hover:scale-110 transition-transform duration-300">
                        {feature.icon}
                      </div>
                    </div>

                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-emerald-700 transition-colors duration-300">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 text-lg mb-4 leading-relaxed">{feature.description}</p>
                      <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-emerald-100 to-green-100 rounded-full">
                        <Star className="w-4 h-4 text-emerald-600 mr-2" />
                        <span className="text-emerald-700 font-semibold text-sm">{feature.stats}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-emerald-50 to-green-50">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-6xl font-bold text-gray-800 mb-6">Real Success Stories</h2>
            <p className="text-xl text-gray-600">See how entrepreneurs are building wealth with VendBoss</p>
          </div>

          <div className="relative h-96">
            {testimonials.map((testimonial, index) => (
              <Card
                key={index}
                className={`absolute inset-0 transition-all duration-700 transform ${index === currentTestimonial
                  ? "opacity-100 scale-100 translate-x-0"
                  : index < currentTestimonial
                    ? "opacity-0 scale-95 -translate-x-full"
                    : "opacity-0 scale-95 translate-x-full"
                  } bg-white/95 backdrop-blur-lg border-2 border-emerald-200 shadow-2xl rounded-3xl`}
              >
                <CardContent className="p-12 text-center h-full flex flex-col justify-center">
                  <div className="w-24 h-24 bg-gradient-to-r from-emerald-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-xl">
                    <span className="text-white font-bold text-2xl">{testimonial.name.slice(0, 2)}</span>
                  </div>

                  <h3 className="text-2xl font-bold text-gray-800 mb-2">{testimonial.name}</h3>
                  <p className="text-emerald-600 font-semibold mb-2">{testimonial.location}</p>
                  <p className="text-gray-600 mb-6">{testimonial.transactions}</p>

                  <div className="text-4xl font-bold text-emerald-600 mb-4">{testimonial.earnings}</div>
                  <p className="text-gray-600 text-sm mb-2">Earned this month</p>

                  <blockquote className="text-xl text-gray-700 italic leading-relaxed max-w-2xl mx-auto">
                    "{testimonial.message}"
                  </blockquote>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="flex justify-center space-x-3 mt-12">
            {testimonials.map((_, index) => (
              <button
                key={index}
                className={`w-4 h-4 rounded-full transition-all duration-300 ${index === currentTestimonial
                  ? "bg-emerald-500 scale-125 shadow-lg"
                  : "bg-emerald-200 hover:bg-emerald-300 hover:scale-110"
                  }`}
                onClick={() => setCurrentTestimonial(index)}
              />
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-emerald-600 to-green-700 relative overflow-hidden">
        <div className="absolute inset-0 bg-[url('/placeholder.svg?height=400&width=800&text=Pattern')] opacity-10"></div>
        <div className="max-w-4xl mx-auto text-center relative z-10">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-8">Ready to Build Your VAS Empire?</h2>
          <p className="text-xl text-emerald-100 mb-12 leading-relaxed">
            Join thousands of entrepreneurs earning with VendBoss. Get your personal vending website and start earning
            in minutes!
          </p>

          <div className="flex flex-col sm:flex-row gap-6 max-w-lg mx-auto mb-12">
            <Input
              placeholder="Enter your phone number"
              className="bg-white/20 border-white/30 text-white placeholder:text-emerald-100 flex-1 py-4 px-6 rounded-xl text-lg backdrop-blur-sm"
            />
            <Button className="bg-white text-emerald-700 hover:bg-emerald-50 font-bold px-8 py-4 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 text-lg">
              <Sparkles className="w-5 h-5 mr-2" />
              Get Started Free
            </Button>
          </div>

          <div className="flex flex-wrap items-center justify-center gap-8 text-emerald-100">
            <div className="flex items-center">
              <Check className="w-5 h-5 text-white mr-3" />
              <span className="font-semibold">Instant setup</span>
            </div>
            <div className="flex items-center">
              <Check className="w-5 h-5 text-white mr-3" />
              <span className="font-semibold">No monthly fees</span>
            </div>
            <div className="flex items-center">
              <Check className="w-5 h-5 text-white mr-3" />
              <span className="font-semibold">24/7 support</span>
            </div>
            <div className="flex items-center">
              <Check className="w-5 h-5 text-white mr-3" />
              <span className="font-semibold">Free training</span>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8 mb-12">
            <div className="md:col-span-2">
              <div className="flex items-center space-x-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center">
                  <span className="text-white font-bold text-xl">V</span>
                </div>
                <div>
                  <span className="text-2xl font-bold">VendBoss</span>
                  <p className="text-xs text-emerald-400 font-semibold">VEND WHILE YOU SLEEP</p>
                </div>
              </div>
              <p className="text-gray-400 text-lg leading-relaxed max-w-md">
                Empowering entrepreneurs across Nigeria to build sustainable VAS businesses with cutting-edge technology
                and unmatched support.
              </p>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4 text-emerald-400">Services</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="/services/airtime" className="hover:text-white transition-colors">
                    Buy Airtime
                  </Link>
                </li>
                <li>
                  <Link href="/services/data" className="hover:text-white transition-colors">
                    Buy Data
                  </Link>
                </li>
                <li>
                  <Link href="/services/electricity" className="hover:text-white transition-colors">
                    Pay Electricity
                  </Link>
                </li>
                <li>
                  <Link href="/services/tv" className="hover:text-white transition-colors">
                    TV Subscription
                  </Link>
                </li>
                <li>
                  <Link href="/services/exam-pins" className="hover:text-white transition-colors">
                    Exam Pins
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4 text-emerald-400">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    API Documentation
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Contact Support
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Training Resources
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 text-sm mb-4 md:mb-0">
              © 2024 VendBoss. All rights reserved. Empowering VAS entrepreneurs across Nigeria.
            </div>
            <div className="flex space-x-6">
              <a href="#" className="text-gray-400 hover:text-emerald-400 transition-colors">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 hover:text-emerald-400 transition-colors">
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </footer>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(5deg); }
        }
        @keyframes gradient {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
        .animate-float {
          animation: float 4s ease-in-out infinite;
        }
        .animate-gradient {
          background-size: 200% 200%;
          animation: gradient 3s ease infinite;
        }
      `}</style>
    </div>
  )
}
